<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:viewModels="clr-namespace:Sanet.MakaMek.Presentation.ViewModels;assembly=Sanet.MakaMek.Presentation"
             mc:Ignorable="d" d:DesignWidth="300" d:DesignHeight="300"
             x:Class="Sanet.MakaMek.Avalonia.Controls.UnitPartSelector"
             x:DataType="viewModels:AimedShotLocationSelectorViewModel">
    
    <UserControl.Styles>
        <Style Selector="Button.bodyPartButton">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="#4CAF50"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Margin" Value="1"/>
            <Setter Property="Padding" Value="2"/>
            <Setter Property="FontSize" Value="10"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="VerticalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
        </Style>
        
        <Style Selector="Button.bodyPartButton:pointerover">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Opacity" Value="0.8"/>
        </Style>
        
        <Style Selector="Button.bodyPartButton:disabled">
            <Setter Property="BorderBrush" Value="#666"/>
            <Setter Property="Foreground" Value="#666"/>
            <Setter Property="Opacity" Value="0.5"/>
        </Style>
    </UserControl.Styles>
    
    <Grid ColumnDefinitions="2*,2*,2*,2*,2*"
          RowDefinitions="*,*,*,*,*,*,*"
          Margin="5">
        <!-- Head -->
        <Button Name="HeadButton" 
                Grid.Row="0" Grid.Column="2" Grid.RowSpan="2"
                Classes="bodyPartButton"
                Command="{Binding SelectPart}"
                CommandParameter="{Binding HeadPart.Location}"
                IsEnabled="{Binding HeadPart.IsSelectable}">
            <StackPanel HorizontalAlignment="Stretch">
                <TextBlock Text="H" Classes="label"/>
                <TextBlock 
                    Text="{Binding HeadPart.HitProbabilityText}" 
                    Foreground="{Binding HeadPart.HitProbability, Converter={StaticResource HitProbabilityColorConverter}}"
                    Classes="probabilityText"/>
            </StackPanel>
        </Button>

        <!-- Left Torso -->
        <Button Name="LeftTorsoButton" 
                Grid.Row="1" Grid.Column="3" Grid.RowSpan="2"
                Classes="bodyPartButton"
                Classes.destroyed="{Binding LeftTorsoPart.IsDestroyed}"
                Command="{Binding SelectPart}"
                CommandParameter="{Binding LeftTorsoPart.Location}"
                IsEnabled="{Binding LeftTorsoPart.IsSelectable}">
            <StackPanel>
                <TextBlock Text="LT" Classes="label"/>
                <TextBlock Text="{Binding LeftTorsoPart.HitProbabilityText}" Classes="probabilityText"/>
            </StackPanel>
        </Button>

        <!-- Center Torso -->
        <Button Name="CenterTorsoButton" 
                Grid.Row="2" Grid.Column="2" Grid.RowSpan="3"
                Classes="bodyPartButton"
                Classes.destroyed="{Binding CenterTorsoPart.IsDestroyed}"
                Command="{Binding SelectPart}"
                CommandParameter="{Binding CenterTorsoPart.Location}"
                IsEnabled="{Binding CenterTorsoPart.IsSelectable}">
            <StackPanel>
                <TextBlock Text="CT" Classes="label"/>
                <TextBlock Text="{Binding CenterTorsoPart.HitProbabilityText}" Classes="probabilityText"/>
            </StackPanel>
        </Button>

        <!-- Right Torso -->
        <Button Name="RightTorsoButton" 
                Grid.Row="1" Grid.Column="1" Grid.RowSpan="3"
                Classes="bodyPartButton"
                Classes.destroyed="{Binding RightTorsoPart.IsDestroyed}"
                Command="{Binding SelectPart}"
                CommandParameter="{Binding RightTorsoPart.Location}"
                IsEnabled="{Binding RightTorsoPart.IsSelectable}">
            <StackPanel>
                <TextBlock Text="RT" Classes="label"/>
                <TextBlock Text="{Binding RightTorsoPart.HitProbabilityText}" Classes="probabilityText"/>
            </StackPanel>
        </Button>

        <!-- Left Arm -->
        <Button Name="LeftArmButton" 
                Grid.Row="2" Grid.Column="4" Grid.RowSpan="3"
                Classes="bodyPartButton"
                Classes.destroyed="{Binding LeftArmPart.IsDestroyed}"
                Command="{Binding SelectPart}"
                CommandParameter="{Binding LeftArmPart.Location}"
                IsEnabled="{Binding LeftArmPart.IsSelectable}">
            <StackPanel>
                <TextBlock Text="LA" Classes="label"/>
                <TextBlock Text="{Binding LeftArmPart.HitProbabilityText}" Classes="probabilityText"/>
            </StackPanel>
        </Button>

        <!-- Right Arm -->
        <Button Name="RightArmButton" 
                Grid.Row="2" Grid.Column="0" Grid.RowSpan="3"
                Classes="bodyPartButton"
                Classes.destroyed="{Binding RightArmPart.IsDestroyed}"
                Command="{Binding SelectPart}"
                CommandParameter="{Binding RightArmPart.Location}"
                IsEnabled="{Binding RightArmPart.IsSelectable}">
            <StackPanel>
                <TextBlock Text="RA" Classes="label"/>
                <TextBlock Text="{Binding RightArmPart.HitProbabilityText}" Classes="probabilityText"/>
            </StackPanel>
        </Button>

        <!-- Left Leg -->
        <Button Name="LeftLegButton" 
                Grid.Row="4" Grid.Column="3" Grid.RowSpan="3"
                Classes="bodyPartButton"
                Classes.destroyed="{Binding LeftLegPart.IsDestroyed}"
                Command="{Binding SelectPart}"
                CommandParameter="{Binding LeftLegPart.Location}"
                IsEnabled="{Binding LeftLegPart.IsSelectable}">
            <StackPanel>
                <TextBlock Text="LL" Classes="label"/>
                <TextBlock Text="{Binding LeftLegPart.HitProbabilityText}" Classes="probabilityText"/>
            </StackPanel>
        </Button>

        <!-- Right Leg -->
        <Button Name="RightLegButton" 
                Grid.Row="4" Grid.Column="1" Grid.RowSpan="3"
                Classes="bodyPartButton"
                Classes.destroyed="{Binding RightLegPart.IsDestroyed}"
                Command="{Binding SelectPart}"
                CommandParameter="{Binding RightLegPart.Location}"
                IsEnabled="{Binding RightLegPart.IsSelectable}">
            <StackPanel>
                <TextBlock Text="RL" Classes="label"/>
                <TextBlock Text="{Binding RightLegPart.HitProbabilityText}" Classes="probabilityText"/>
            </StackPanel>
        </Button>
    </Grid>
</UserControl>
